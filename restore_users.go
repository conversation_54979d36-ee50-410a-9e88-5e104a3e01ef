package main

import (
	"fmt"
	"log"
	"projectm2/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 恢复被软删除的用户
	fmt.Println("=== 恢复被软删除的用户 ===")
	
	// 查询被软删除的用户
	var deletedUsers []models.User
	err = db.Unscoped().Where("deleted_at IS NOT NULL").Find(&deletedUsers).Error
	if err != nil {
		log.Fatal("查询被删除用户失败:", err)
	}

	fmt.Printf("找到 %d 个被删除的用户\n", len(deletedUsers))
	
	for _, user := range deletedUsers {
		fmt.Printf("恢复用户: %s (%s)\n", user.Username, user.FullName)
		
		// 恢复用户（清除deleted_at字段）
		err = db.Unscoped().Model(&user).Update("deleted_at", nil).Error
		if err != nil {
			log.Printf("恢复用户 %s 失败: %v", user.Username, err)
		} else {
			fmt.Printf("✓ 用户 %s 恢复成功\n", user.Username)
		}
	}

	// 验证恢复结果
	fmt.Println("\n=== 验证恢复结果 ===")
	var allUsers []models.User
	err = db.Find(&allUsers).Error
	if err != nil {
		log.Fatal("查询用户失败:", err)
	}

	fmt.Printf("当前活跃用户数量: %d\n", len(allUsers))
	for _, user := range allUsers {
		fmt.Printf("用户: %s (%s) - %s\n", user.Username, user.FullName, user.Role)
	}
}
