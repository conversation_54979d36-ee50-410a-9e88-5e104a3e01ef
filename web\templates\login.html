<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ProjectM2 - 现代化项目管理系统">
    <title>{{.title}} - ProjectM2</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="/static/css/tailwind-local.css">
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 全新商用级登录页面 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f0f23;
            overflow: hidden;
        }

        /* 主容器 - 分屏设计 */
        .login-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* 左侧装饰区域 */
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            animation: shimmer 8s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        /* 左侧品牌区域 */
        .brand-section {
            text-align: center;
            z-index: 10;
            position: relative;
        }

        .brand-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .brand-logo svg {
            width: 60px;
            height: 60px;
            color: white;
        }

        .brand-title {
            font-size: 3rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .brand-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            line-height: 1.6;
        }

        /* 右侧登录区域 */
        .login-right {
            flex: 1;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            position: relative;
        }
        /* 登录卡片 */
        .login-card {
            width: 100%;
            max-width: 480px;
            background: white;
            border-radius: 24px;
            padding: 3rem;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 8px 25px rgba(0, 0, 0, 0.06);
            position: relative;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #64748b;
            font-size: 1rem;
            font-weight: 400;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #ffffff;
            font-size: 1rem;
            color: #1f2937;
            transition: all 0.2s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-input::placeholder {
            color: #a0aec0;
            font-weight: 400;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            letter-spacing: 0.025em;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.025em;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.3);
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }
        
        .brand-logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 24px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        .error-message {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 1px solid #f87171;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .success-message {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 1px solid #34d399;
            color: #059669;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            animation: slideDown 0.4s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-12px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .theme-toggle {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 20;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        /* 测试账户样式 */
        .test-accounts {
            background: #f8fafc;
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
        }

        .account-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .account-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;
        }

        .account-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        .account-role {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.25rem;
        }

        .account-info {
            font-size: 0.75rem;
            color: #6b7280;
            font-family: 'Courier New', monospace;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
            }

            .login-left {
                min-height: 40vh;
                padding: 2rem;
            }

            .brand-title {
                font-size: 2rem;
            }

            .brand-subtitle {
                font-size: 1rem;
            }

            .login-right {
                padding: 2rem 1.5rem;
            }

            .login-card {
                padding: 2rem;
            }

            .theme-toggle {
                top: 16px;
                right: 16px;
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .login-left {
                min-height: 30vh;
                padding: 1.5rem;
            }

            .brand-logo {
                width: 80px;
                height: 80px;
            }

            .brand-title {
                font-size: 1.75rem;
            }

            .login-card {
                padding: 1.5rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }

        .default-accounts {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            margin-top: 24px;
        }

        .default-accounts h4 {
            font-size: 14px;
            font-weight: 600;
            color: #475569;
            margin-bottom: 12px;
            text-align: center;
        }

        .account-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 13px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .account-item:hover {
            background: #f8fafc;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .account-item:last-child {
            margin-bottom: 0;
        }

        .account-role {
            font-weight: 600;
            color: #374151;
        }

        .account-credentials {
            font-family: 'Monaco', 'Menlo', monospace;
            color: #6b7280;
            font-size: 12px;
        }
        
        /* 输入框增强样式 */
        .input-focused label {
            color: var(--primary-600);
            transform: translateY(-2px);
        }

        .form-input.border-green-500 {
            border-color: var(--success-500);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-input.border-red-500 {
            border-color: var(--danger-500);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        /* 暗色主题适配 */
        [data-theme="dark"] .login-container {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }

        [data-theme="dark"] .login-card {
            background: rgba(26, 32, 44, 0.95);
            border-color: rgba(74, 85, 104, 0.3);
        }

        [data-theme="dark"] .brand-logo {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }

        [data-theme="dark"] .form-input {
            background: rgba(45, 55, 72, 0.8);
            border-color: #4a5568;
            color: #e2e8f0;
        }

        [data-theme="dark"] .form-input:focus {
            background: rgba(45, 55, 72, 1);
            border-color: #4299e1;
            box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.1);
        }

        [data-theme="dark"] .form-input::placeholder {
            color: #a0aec0;
        }

        [data-theme="dark"] .form-label {
            color: #e2e8f0;
        }

        [data-theme="dark"] h1 {
            color: #f7fafc;
        }

        [data-theme="dark"] p {
            color: #cbd5e0;
        }

        [data-theme="dark"] .default-accounts {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            border-color: #4a5568;
        }

        [data-theme="dark"] .default-accounts h4 {
            color: #e2e8f0;
        }

        [data-theme="dark"] .account-item {
            background: rgba(26, 32, 44, 0.8);
            border: 1px solid #4a5568;
        }

        [data-theme="dark"] .account-role {
            color: #f7fafc;
        }

        [data-theme="dark"] .account-credentials {
            color: #a0aec0;
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <button id="themeToggle" class="theme-toggle" title="切换主题">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
        </svg>
    </button>

    <!-- 全新分屏登录界面 -->
    <div class="login-wrapper">
        <!-- 左侧品牌区域 -->
        <div class="login-left">
            <div class="brand-section">
                <div class="brand-logo">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                    </svg>
                </div>
                <h1 class="brand-title">ProjectM</h1>
                <p class="brand-subtitle">现代化项目管理平台<br>让团队协作更高效</p>
            </div>
        </div>

        <!-- 右侧登录区域 -->
        <div class="login-right">
            <div class="login-card">
                <div class="login-header">
                    <h2 class="login-title">欢迎回来</h2>
                    <p class="login-subtitle">请登录您的账户继续使用</p>
                </div>

                <!-- 错误/成功消息 -->
                <div id="message-container" class="mb-6 hidden">
                    <div id="message" class="p-4 rounded-xl text-sm font-medium"></div>
                </div>

                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-6">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            required
                            class="form-input"
                            placeholder="请输入您的用户名"
                            autocomplete="username"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            class="form-input"
                            placeholder="请输入您的密码"
                            autocomplete="current-password"
                        >
                    </div>

                    <button
                        type="submit"
                        id="loginBtn"
                        class="login-btn"
                    >
                        <span id="loginText">登录</span>
                        <div id="loginSpinner" class="loading-spinner hidden"></div>
                    </button>
                </form>
            

                <!-- 底部信息 -->
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-600 mb-6">
                        还没有账户？
                        <a href="#" class="text-blue-600 hover:text-blue-700 font-semibold transition-colors">联系管理员</a>
                    </p>

                    <!-- 测试账户 -->
                    <div class="test-accounts">
                        <h4 class="text-sm font-semibold text-gray-700 mb-3">快速登录测试账户</h4>
                        <div class="account-grid">
                            <div class="account-card" onclick="quickFill('admin', 'admin123')">
                                <div class="account-role">管理员</div>
                                <div class="account-info">admin / admin123</div>
                            </div>
                            <div class="account-card" onclick="quickFill('manager', 'manager123')">
                                <div class="account-role">项目经理</div>
                                <div class="account-info">manager / manager123</div>
                            </div>
                            <div class="account-card" onclick="quickFill('developer1', 'dev123')">
                                <div class="account-role">开发者</div>
                                <div class="account-info">developer1 / dev123</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="/static/js/login.js"></script>
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 使用增强的验证
            if (!window.LoginPage.validateForm()) {
                window.LoginPage.showMessage('请检查输入信息', 'error');
                return;
            }

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // 使用增强的登录处理
            await window.LoginPage.handleLogin(username, password);
        });

        // 显示消息（兼容性函数）
        function showMessage(message, type) {
            window.LoginPage.showMessage(message, type);
        }

        // 快速填充账户信息
        function quickFill(username, password) {
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // 添加填充动画
            usernameInput.style.transform = 'scale(1.05)';
            passwordInput.style.transform = 'scale(1.05)';

            setTimeout(() => {
                usernameInput.value = username;
                passwordInput.value = password;

                // 触发输入事件以更新样式
                usernameInput.dispatchEvent(new Event('input'));
                passwordInput.dispatchEvent(new Event('input'));

                // 恢复缩放
                usernameInput.style.transform = 'scale(1)';
                passwordInput.style.transform = 'scale(1)';

                // 显示提示
                showMessage('账户信息已填充，点击登录按钮继续', 'success');
            }, 150);
        }

        // 主题切换
        document.getElementById('themeToggle').addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            window.LoginPage.setTheme(newTheme);

            // 更新按钮图标
            updateThemeIcon(newTheme);
        });

        // 更新主题图标
        function updateThemeIcon(theme) {
            const button = document.getElementById('themeToggle');
            const icon = button.querySelector('svg path');

            if (theme === 'dark') {
                // 太阳图标
                icon.setAttribute('d', 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z');
            } else {
                // 月亮图标
                icon.setAttribute('d', 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z');
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            updateThemeIcon(currentTheme);

            // 登录卡片入场动画
            const loginCard = document.querySelector('.login-card');
            loginCard.style.opacity = '0';
            loginCard.style.transform = 'translateY(30px) scale(0.95)';

            setTimeout(() => {
                loginCard.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                loginCard.style.opacity = '1';
                loginCard.style.transform = 'translateY(0) scale(1)';
            }, 150);
        });
    </script>
</body>
</html>
