package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type User struct {
	ID        string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username  string     `json:"username"`
	FullName  string     `json:"full_name"`
	Role      string     `json:"role"`
	IsActive  bool       `json:"is_active"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询所有用户（包括软删除的）
	var allUsers []User
	err = db.Unscoped().Find(&allUsers).Error
	if err != nil {
		log.Fatal("查询所有用户失败:", err)
	}

	fmt.Printf("=== 所有用户的软删除状态 ===\n")
	for _, user := range allUsers {
		deletedStatus := "正常"
		if user.DeletedAt != nil {
			deletedStatus = fmt.Sprintf("已删除 (%s)", user.DeletedAt.Format("2006-01-02 15:04:05"))
		}
		fmt.Printf("用户: %s (%s) - %s - %s\n", 
			user.Username, user.FullName, user.Role, deletedStatus)
	}

	// 查询未删除的用户
	var activeUsers []User
	err = db.Find(&activeUsers).Error
	if err != nil {
		log.Fatal("查询未删除用户失败:", err)
	}

	fmt.Printf("\n=== 未删除的用户 ===\n")
	fmt.Printf("数量: %d\n", len(activeUsers))
	for _, user := range activeUsers {
		fmt.Printf("用户: %s (%s) - %s\n", 
			user.Username, user.FullName, user.Role)
	}

	// 查询已删除的用户
	var deletedUsers []User
	err = db.Unscoped().Where("deleted_at IS NOT NULL").Find(&deletedUsers).Error
	if err != nil {
		log.Fatal("查询已删除用户失败:", err)
	}

	fmt.Printf("\n=== 已删除的用户 ===\n")
	fmt.Printf("数量: %d\n", len(deletedUsers))
	for _, user := range deletedUsers {
		fmt.Printf("用户: %s (%s) - %s - 删除时间: %s\n", 
			user.Username, user.FullName, user.Role, user.DeletedAt.Format("2006-01-02 15:04:05"))
	}
}
