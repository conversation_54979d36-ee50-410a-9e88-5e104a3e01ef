package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRole 用户角色枚举
type UserRole string

const (
	RoleAdmin     UserRole = "admin"
	RoleManager   UserRole = "manager"
	RoleDeveloper UserRole = "developer"
	RoleViewer    UserRole = "viewer"
)

// User 用户模型
type User struct {
	ID          string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username    string         `json:"username" gorm:"uniqueIndex;not null;size:50"`
	Email       string         `json:"email" gorm:"uniqueIndex;not null;size:100"`
	Password    string         `json:"-" gorm:"size:255;default:''"` // 密码字段，不在JSON中返回
	FullName    string         `json:"full_name" gorm:"not null;size:100"`
	Avatar      string         `json:"avatar" gorm:"size:500"`
	Role        UserRole       `json:"role" gorm:"not null;default:'developer'"`
	IsActive    bool           `json:"is_active" gorm:"not null;default:true"`
	LastLoginAt *time.Time     `json:"last_login_at"`
	LoginCount  int            `json:"login_count" gorm:"default:0"` // 登录次数
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	AssignedTasks []Task `json:"assigned_tasks,omitempty" gorm:"foreignKey:AssignedTo"`
	CreatedTasks  []Task `json:"created_tasks,omitempty" gorm:"foreignKey:CreatedBy"`
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}

// GetRoleDisplayName 获取角色显示名称
func (r UserRole) GetDisplayName() string {
	switch r {
	case RoleAdmin:
		return "管理员"
	case RoleManager:
		return "项目经理"
	case RoleDeveloper:
		return "开发者"
	case RoleViewer:
		return "查看者"
	default:
		return "开发者"
	}
}

// GetRoleColor 获取角色颜色类
func (r UserRole) GetRoleColor() string {
	switch r {
	case RoleAdmin:
		return "text-red-600 bg-red-100"
	case RoleManager:
		return "text-blue-600 bg-blue-100"
	case RoleDeveloper:
		return "text-green-600 bg-green-100"
	case RoleViewer:
		return "text-gray-600 bg-gray-100"
	default:
		return "text-gray-600 bg-gray-100"
	}
}

// CanAssignTasks 检查用户是否可以分配任务
func (u *User) CanAssignTasks() bool {
	return u.Role == RoleAdmin || u.Role == RoleManager
}

// CanDeleteTasks 检查用户是否可以删除任务
func (u *User) CanDeleteTasks() bool {
	return u.Role == RoleAdmin || u.Role == RoleManager
}

// CanViewAllTasks 检查用户是否可以查看所有任务
func (u *User) CanViewAllTasks() bool {
	return u.Role == RoleAdmin || u.Role == RoleManager
}

// GetAvatarURL 获取头像URL
func (u *User) GetAvatarURL() string {
	if u.Avatar != "" {
		return u.Avatar
	}
	// 返回空字符串，让前端生成默认头像
	return ""
}

// UserCreateRequest 创建用户请求结构
type UserCreateRequest struct {
	Username string   `json:"username" binding:"required,min=3,max=50"`
	Email    string   `json:"email" binding:"required,email"`
	Password string   `json:"password" binding:"required,min=6,max=100"` // 添加密码字段
	FullName string   `json:"full_name" binding:"required,min=1,max=100"`
	Role     UserRole `json:"role"`
	Avatar   string   `json:"avatar"`
}

// UserUpdateRequest 更新用户请求结构
type UserUpdateRequest struct {
	FullName string   `json:"full_name" binding:"min=1,max=100"`
	Role     UserRole `json:"role"`
	Avatar   string   `json:"avatar"`
	IsActive *bool    `json:"is_active,omitempty"` // 使用指针，这样可以区分是否提供了该字段
	Password string   `json:"password,omitempty"`  // 可选的密码字段，用于重置密码
}

// UserListResponse 用户列表响应结构
type UserListResponse struct {
	ID       string   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	FullName string   `json:"full_name"`
	Avatar   string   `json:"avatar"`
	Role     UserRole `json:"role"`
	IsActive bool     `json:"is_active"`
}

// ToListResponse 转换为列表响应格式
func (u *User) ToListResponse() UserListResponse {
	return UserListResponse{
		ID:       u.ID,
		Username: u.Username,
		Email:    u.Email,
		FullName: u.FullName,
		Avatar:   u.GetAvatarURL(),
		Role:     u.Role,
		IsActive: u.IsActive,
	}
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	User    *User  `json:"user,omitempty"`
}
