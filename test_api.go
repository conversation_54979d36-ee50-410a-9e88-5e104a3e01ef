package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
)

func main() {
	// 测试管理员API
	fmt.Println("=== 测试 /api/users (管理员API) ===")
	resp1, err := http.Get("http://localhost:8090/api/users")
	if err != nil {
		log.Printf("请求失败: %v", err)
	} else {
		defer resp1.Body.Close()
		body1, _ := io.ReadAll(resp1.Body)
		fmt.Printf("状态码: %d\n", resp1.StatusCode)
		fmt.Printf("响应: %s\n", string(body1))

		if resp1.StatusCode == 200 {
			var data map[string]interface{}
			json.Unmarshal(body1, &data)
			if users, ok := data["users"].([]interface{}); ok {
				fmt.Printf("用户数量: %d\n", len(users))
				for i, user := range users {
					if userMap, ok := user.(map[string]interface{}); ok {
						fmt.Printf("用户%d: %s (%s) - %s\n", i+1,
							userMap["username"], userMap["full_name"], userMap["role"])
					}
				}
			}
		}
	}

	fmt.Println("\n=== 测试 /api/users/active (普通API) ===")
	resp2, err := http.Get("http://localhost:8090/api/users/active")
	if err != nil {
		log.Printf("请求失败: %v", err)
	} else {
		defer resp2.Body.Close()
		body2, _ := io.ReadAll(resp2.Body)
		fmt.Printf("状态码: %d\n", resp2.StatusCode)
		fmt.Printf("响应: %s\n", string(body2))

		if resp2.StatusCode == 200 {
			var data map[string]interface{}
			json.Unmarshal(body2, &data)
			if users, ok := data["users"].([]interface{}); ok {
				fmt.Printf("用户数量: %d\n", len(users))
				for i, user := range users {
					if userMap, ok := user.(map[string]interface{}); ok {
						fmt.Printf("用户%d: %s (%s) - %s\n", i+1,
							userMap["username"], userMap["full_name"], userMap["role"])
					}
				}
			}
		}
	}
}
