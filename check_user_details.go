package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type User struct {
	ID        string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username  string     `json:"username" gorm:"uniqueIndex;not null;size:50"`
	Email     string     `json:"email" gorm:"uniqueIndex;not null;size:100"`
	FullName  string     `json:"full_name" gorm:"not null;size:100"`
	Role      string     `json:"role" gorm:"not null;size:20"`
	IsActive  bool       `json:"is_active" gorm:"not null;default:true"`
	Avatar    string     `json:"avatar" gorm:"size:255"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"index"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询所有用户（包括软删除的）
	var allUsers []User
	err = db.Unscoped().Find(&allUsers).Error
	if err != nil {
		log.Fatal("查询所有用户失败:", err)
	}

	fmt.Printf("=== 所有用户数据（包括软删除） ===\n")
	fmt.Printf("总用户数: %d\n", len(allUsers))
	fmt.Println("Username\t\tFullName\t\tRole\t\tIsActive\tDeletedAt")
	fmt.Println("--------------------------------------------------------------------")
	for _, user := range allUsers {
		deletedStatus := "否"
		if user.DeletedAt != nil {
			deletedStatus = "是"
		}
		fmt.Printf("%s\t\t%s\t\t%s\t\t%t\t\t%s\n", 
			user.Username, user.FullName, user.Role, user.IsActive, deletedStatus)
	}

	// 查询未软删除的用户
	var activeUsers []User
	err = db.Find(&activeUsers).Error
	if err != nil {
		log.Fatal("查询活跃用户失败:", err)
	}

	fmt.Printf("\n=== 未软删除的用户数据 ===\n")
	fmt.Printf("用户数: %d\n", len(activeUsers))
	fmt.Println("Username\t\tFullName\t\tRole\t\tIsActive")
	fmt.Println("------------------------------------------------------------")
	for _, user := range activeUsers {
		fmt.Printf("%s\t\t%s\t\t%s\t\t%t\n", 
			user.Username, user.FullName, user.Role, user.IsActive)
	}

	// 查询is_active=true的用户
	var reallyActiveUsers []User
	err = db.Where("is_active = ?", true).Find(&reallyActiveUsers).Error
	if err != nil {
		log.Fatal("查询真正活跃用户失败:", err)
	}

	fmt.Printf("\n=== is_active=true的用户数据 ===\n")
	fmt.Printf("用户数: %d\n", len(reallyActiveUsers))
	fmt.Println("Username\t\tFullName\t\tRole")
	fmt.Println("--------------------------------------------")
	for _, user := range reallyActiveUsers {
		fmt.Printf("%s\t\t%s\t\t%s\n", 
			user.Username, user.FullName, user.Role)
	}
}
