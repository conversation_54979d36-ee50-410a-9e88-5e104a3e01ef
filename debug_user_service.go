package main

import (
	"fmt"
	"log"
	"projectm2/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 模拟UserService的GetAllUsers方法
	fmt.Println("=== 模拟 GetAllUsers 方法 ===")
	var users []models.User
	err = db.Order("is_active DESC, full_name").Find(&users).Error
	if err != nil {
		log.Fatal("查询失败:", err)
	}

	fmt.Printf("查询到的用户数量: %d\n", len(users))
	for i, user := range users {
		fmt.Printf("用户%d: ID=%s, Username=%s, FullName=%s, Role=%s, IsActive=%t\n", 
			i+1, user.ID, user.Username, user.FullName, user.Role, user.IsActive)
	}

	// 转换为UserListResponse格式
	var userList []models.UserListResponse
	for _, user := range users {
		userList = append(userList, user.ToListResponse())
	}

	fmt.Printf("\n转换后的用户列表数量: %d\n", len(userList))
	for i, user := range userList {
		fmt.Printf("响应用户%d: ID=%s, Username=%s, FullName=%s, Role=%s, IsActive=%t\n", 
			i+1, user.ID, user.Username, user.FullName, user.Role, user.IsActive)
	}

	// 模拟GetActiveUsers方法
	fmt.Println("\n=== 模拟 GetActiveUsers 方法 ===")
	var activeUsers []models.User
	err = db.Where("is_active = ?", true).Order("full_name").Find(&activeUsers).Error
	if err != nil {
		log.Fatal("查询活跃用户失败:", err)
	}

	fmt.Printf("查询到的活跃用户数量: %d\n", len(activeUsers))
	for i, user := range activeUsers {
		fmt.Printf("活跃用户%d: ID=%s, Username=%s, FullName=%s, Role=%s\n", 
			i+1, user.ID, user.Username, user.FullName, user.Role)
	}

	// 转换为UserListResponse格式
	var activeUserList []models.UserListResponse
	for _, user := range activeUsers {
		activeUserList = append(activeUserList, user.ToListResponse())
	}

	fmt.Printf("\n转换后的活跃用户列表数量: %d\n", len(activeUserList))
	for i, user := range activeUserList {
		fmt.Printf("响应活跃用户%d: ID=%s, Username=%s, FullName=%s, Role=%s\n", 
			i+1, user.ID, user.Username, user.FullName, user.Role)
	}
}
