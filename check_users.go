package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type User struct {
	ID       string `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username string `json:"username" gorm:"uniqueIndex;not null;size:50"`
	Email    string `json:"email" gorm:"uniqueIndex;not null;size:100"`
	FullName string `json:"full_name" gorm:"not null;size:100"`
	Role     string `json:"role" gorm:"not null;size:20"`
	IsActive bool   `json:"is_active" gorm:"not null;default:true"`
	Avatar   string `json:"avatar" gorm:"size:255"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询所有用户
	var allUsers []User
	err = db.Find(&allUsers).Error
	if err != nil {
		log.Fatal("查询所有用户失败:", err)
	}

	fmt.Printf("=== 所有用户数据 ===\n")
	fmt.Printf("总用户数: %d\n", len(allUsers))
	fmt.Println("ID\t\t\t\t\tUsername\t\tFullName\t\tRole\t\tIsActive")
	fmt.Println("------------------------------------------------------------")
	for _, user := range allUsers {
		fmt.Printf("%s\t%s\t\t%s\t\t%s\t\t%t\n", 
			user.ID, user.Username, user.FullName, user.Role, user.IsActive)
	}

	// 查询活跃用户
	var activeUsers []User
	err = db.Where("is_active = ?", true).Find(&activeUsers).Error
	if err != nil {
		log.Fatal("查询活跃用户失败:", err)
	}

	fmt.Printf("\n=== 活跃用户数据 ===\n")
	fmt.Printf("活跃用户数: %d\n", len(activeUsers))
	fmt.Println("ID\t\t\t\t\tUsername\t\tFullName\t\tRole")
	fmt.Println("------------------------------------------------------------")
	for _, user := range activeUsers {
		fmt.Printf("%s\t%s\t\t%s\t\t%s\n", 
			user.ID, user.Username, user.FullName, user.Role)
	}

	// 按角色统计
	var adminCount, devCount int64
	db.Model(&User{}).Where("role = ? AND is_active = ?", "admin", true).Count(&adminCount)
	db.Model(&User{}).Where("role = ? AND is_active = ?", "developer", true).Count(&devCount)

	fmt.Printf("\n=== 角色统计 ===\n")
	fmt.Printf("活跃管理员: %d\n", adminCount)
	fmt.Printf("活跃开发者: %d\n", devCount)
}
