<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast 关闭按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button.success { background: #10b981; }
        .test-button.success:hover { background: #059669; }
        .test-button.error { background: #ef4444; }
        .test-button.error:hover { background: #dc2626; }
        .test-button.warning { background: #f59e0b; }
        .test-button.warning:hover { background: #d97706; }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Toast 关闭按钮修复测试</h1>
        
        <div class="description">
            <strong>测试说明：</strong><br>
            点击下面的按钮会显示不同类型的Toast通知。每个Toast通知都应该有一个可点击的关闭按钮（×）。
            点击关闭按钮应该能立即关闭Toast通知。
        </div>

        <div style="text-align: center;">
            <button class="test-button success" onclick="testToast('success')">
                ✅ 测试成功Toast
            </button>
            
            <button class="test-button error" onclick="testToast('error')">
                ❌ 测试错误Toast
            </button>
            
            <button class="test-button warning" onclick="testToast('warning')">
                ⚠️ 测试警告Toast
            </button>
            
            <button class="test-button" onclick="testToast('info')">
                ℹ️ 测试信息Toast
            </button>
            
            <button class="test-button" onclick="testLongToast()">
                📝 测试长时间Toast (不自动消失)
            </button>
            
            <button class="test-button" onclick="testMultipleToasts()">
                🔄 测试多个Toast
            </button>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border-radius: 6px;">
            <strong>预期行为：</strong>
            <ul>
                <li>每个Toast都应该在右上角显示</li>
                <li>每个Toast都应该有一个可点击的 × 关闭按钮</li>
                <li>点击关闭按钮应该立即关闭Toast</li>
                <li>Toast应该有滑入滑出动画效果</li>
                <li>除了"长时间Toast"外，其他Toast应该在3秒后自动消失</li>
            </ul>
        </div>
    </div>

    <script>
        // 全局 toast 调用计数器，防止递归
        window._toastCallCount = 0;

        // 显示提示消息 - 修复版本
        function showToast(message, type = 'success', duration = 3000) {
            // 严格的递归防护
            if (window._toastCallCount > 0) {
                console.warn('showToast 递归调用被阻止:', message);
                return;
            }

            window._toastCallCount++;

            try {
                // 创建toast元素
                const toast = document.createElement('div');
                const toastId = 'toast_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
                toast.id = toastId;

                // 设置样式和内容
                const colors = {
                    success: '#10b981',
                    error: '#ef4444',
                    warning: '#f59e0b',
                    info: '#3b82f6'
                };

                // 创建toast内容容器
                const toastContent = document.createElement('div');
                toastContent.style.cssText = `
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 12px;
                `;

                // 创建消息文本
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                messageSpan.style.cssText = `
                    flex: 1;
                    word-wrap: break-word;
                `;

                // 创建关闭按钮
                const closeButton = document.createElement('button');
                closeButton.innerHTML = '×';
                closeButton.style.cssText = `
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    cursor: pointer;
                    padding: 0;
                    margin: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0.8;
                    transition: opacity 0.2s ease;
                `;

                // 关闭按钮悬停效果
                closeButton.addEventListener('mouseenter', () => {
                    closeButton.style.opacity = '1';
                });
                closeButton.addEventListener('mouseleave', () => {
                    closeButton.style.opacity = '0.8';
                });

                // 关闭按钮点击事件
                closeButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('关闭按钮被点击，Toast ID:', toastId);
                    if (toast.parentNode) {
                        toast.style.transform = 'translateX(100%)';
                        setTimeout(() => {
                            if (toast.parentNode) {
                                toast.remove();
                                console.log('Toast已移除:', toastId);
                            }
                        }, 300);
                    }
                });

                // 组装toast内容
                toastContent.appendChild(messageSpan);
                toastContent.appendChild(closeButton);
                toast.appendChild(toastContent);

                // 设置toast样式
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${colors[type] || colors.info};
                    color: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                    max-width: 350px;
                    word-wrap: break-word;
                `;

                document.body.appendChild(toast);
                console.log('Toast已创建并添加到页面:', toastId);

                // 显示动画
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 10);

                // 自动移除
                if (duration > 0) {
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.style.transform = 'translateX(100%)';
                            setTimeout(() => {
                                if (toast.parentNode) {
                                    toast.remove();
                                    console.log('Toast自动移除:', toastId);
                                }
                            }, 300);
                        }
                    }, duration);
                }

                return toastId;
            } catch (error) {
                console.error('showToast 执行错误:', error);
            } finally {
                // 重置计数器
                window._toastCallCount = 0;
            }
        }

        // 测试函数
        function testToast(type) {
            const messages = {
                success: '✅ 操作成功！这是一个成功提示。',
                error: '❌ 操作失败！这是一个错误提示。',
                warning: '⚠️ 注意！这是一个警告提示。',
                info: 'ℹ️ 提示信息：这是一个普通信息提示。'
            };
            
            showToast(messages[type] || messages.info, type);
        }

        function testLongToast() {
            showToast('📝 这是一个长时间显示的Toast，不会自动消失。请点击关闭按钮来关闭它。', 'info', 0);
        }

        function testMultipleToasts() {
            showToast('第一个Toast', 'success');
            setTimeout(() => showToast('第二个Toast', 'warning'), 500);
            setTimeout(() => showToast('第三个Toast', 'error'), 1000);
        }

        // 页面加载完成后显示欢迎消息
        window.addEventListener('load', () => {
            setTimeout(() => {
                showToast('🎉 Toast测试页面加载完成！请点击按钮测试关闭功能。', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
