package main

import (
	"fmt"
	"log"
	"projectm2/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("projectm2.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 查询所有用户
	var allUsers []models.User
	err = db.Find(&allUsers).Error
	if err != nil {
		log.Fatal("查询用户失败:", err)
	}

	// 统计数据
	totalUsers := len(allUsers)
	activeUsers := 0
	adminUsers := 0
	managerUsers := 0
	developerUsers := 0

	for _, user := range allUsers {
		if user.IsActive {
			activeUsers++
		}
		
		switch user.Role {
		case models.RoleAdmin:
			adminUsers++
		case models.RoleManager:
			managerUsers++
		case models.RoleDeveloper:
			developerUsers++
		}
	}

	fmt.Printf("=== 用户统计数据 ===\n")
	fmt.Printf("总用户数: %d\n", totalUsers)
	fmt.Printf("活跃用户数: %d\n", activeUsers)
	fmt.Printf("管理员数: %d\n", adminUsers)
	fmt.Printf("项目经理数: %d\n", managerUsers)
	fmt.Printf("开发者数: %d\n", developerUsers)

	fmt.Printf("\n=== 用户详情 ===\n")
	for _, user := range allUsers {
		fmt.Printf("用户: %s (%s) - %s - 活跃:%t\n", 
			user.Username, user.FullName, user.Role, user.IsActive)
	}
}
