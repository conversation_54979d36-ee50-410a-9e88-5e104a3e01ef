package handlers

import (
	"net/http"
	"projectm2/internal/models"
	"projectm2/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService    *services.AuthService
	sessionService *services.SessionService
}

// NewAuthHandler 创建新的认证处理器实例
func NewAuthHandler(db *gorm.DB, sessionService *services.SessionService) *AuthHandler {
	return &AuthHandler{
		authService:    services.NewAuthService(db),
		sessionService: sessionService,
	}
}

// LoginPage 登录页面
func (h *AuthHandler) LoginPage(c *gin.Context) {
	// 检查是否已经登录
	if sessionID, err := c.<PERSON>("session_id"); err == nil {
		if _, err := h.sessionService.GetSession(sessionID); err == nil {
			c.Redirect(http.StatusFound, "/")
			return
		}
	}

	c.HTML(http.StatusOK, "login.html", gin.H{
		"title": "用户登录",
	})
}

// Login 用户登录API
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.LoginResponse{
			Success: false,
			Message: "请求参数错误",
		})
		return
	}

	// 验证用户登录
	user, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.LoginResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}

	// 创建会话
	session, err := h.sessionService.CreateSession(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.LoginResponse{
			Success: false,
			Message: "创建会话失败",
		})
		return
	}

	// 设置Cookie
	c.SetCookie("session_id", session.ID, 86400, "/", "", false, true) // 24小时过期

	c.JSON(http.StatusOK, models.LoginResponse{
		Success: true,
		Message: "登录成功",
		User:    user,
	})
}

// Logout 用户登出API
func (h *AuthHandler) Logout(c *gin.Context) {
	// 获取会话ID
	sessionID, err := c.Cookie("session_id")
	if err == nil {
		// 删除会话
		h.sessionService.DeleteSession(sessionID)
	}

	// 清除Cookie
	c.SetCookie("session_id", "", -1, "/", "", false, true)

	// 根据请求类型返回不同响应
	if c.Request.Header.Get("Content-Type") == "application/json" ||
		c.Request.Header.Get("Accept") == "application/json" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "登出成功",
		})
	} else {
		c.Redirect(http.StatusFound, "/login")
	}
}

// Register 用户注册API
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证密码强度
	if err := h.authService.ValidatePassword(req.Password); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 创建用户
	user, err := h.authService.CreateUserWithPassword(req)
	if err != nil {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "注册成功",
		"user":    user,
	})
}

// ChangePassword 修改密码API
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	// 获取当前用户
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "请先登录",
		})
		return
	}

	session := userInterface.(*services.Session)
	user := session.User

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}

	// 验证新密码强度
	if err := h.authService.ValidatePassword(req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 修改密码
	if err := h.authService.ChangePassword(user.ID, req.OldPassword, req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}

// GetCurrentUser 获取当前用户信息API
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "请先登录",
		})
		return
	}

	// 认证中间件存储的是 *models.User，不是 *services.Session
	user := userInterface.(*models.User)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"user":    user,
	})
}
