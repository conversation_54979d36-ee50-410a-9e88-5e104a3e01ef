package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/cookiejar"
)

func main() {
	// 创建带cookie的HTTP客户端
	jar, _ := cookiejar.New(nil)
	client := &http.Client{Jar: jar}

	// 1. 先登录获取session
	fmt.Println("=== 登录 ===")
	loginData := map[string]string{
		"username": "admin",
		"password": "admin123",
	}
	loginJSON, _ := json.Marshal(loginData)
	
	loginResp, err := client.Post("http://localhost:8090/api/auth/login", 
		"application/json", bytes.NewBuffer(loginJSON))
	if err != nil {
		log.Fatal("登录失败:", err)
	}
	defer loginResp.Body.Close()
	
	loginBody, _ := io.ReadAll(loginResp.Body)
	fmt.Printf("登录状态码: %d\n", loginResp.StatusCode)
	fmt.Printf("登录响应: %s\n", string(loginBody))

	if loginResp.StatusCode != 200 {
		log.Fatal("登录失败，无法继续测试")
	}

	// 2. 测试管理员API
	fmt.Println("\n=== 测试 /api/users (管理员API) ===")
	resp1, err := client.Get("http://localhost:8090/api/users")
	if err != nil {
		log.Printf("请求失败: %v", err)
	} else {
		defer resp1.Body.Close()
		body1, _ := io.ReadAll(resp1.Body)
		fmt.Printf("状态码: %d\n", resp1.StatusCode)
		fmt.Printf("响应: %s\n", string(body1))
		
		if resp1.StatusCode == 200 {
			var data map[string]interface{}
			json.Unmarshal(body1, &data)
			if users, ok := data["users"].([]interface{}); ok {
				fmt.Printf("用户数量: %d\n", len(users))
				for i, user := range users {
					if userMap, ok := user.(map[string]interface{}); ok {
						fmt.Printf("用户%d: %s (%s) - %s - 活跃:%v\n", i+1, 
							userMap["username"], userMap["full_name"], userMap["role"], userMap["is_active"])
					}
				}
			}
		}
	}

	// 3. 测试普通API
	fmt.Println("\n=== 测试 /api/users/active (普通API) ===")
	resp2, err := client.Get("http://localhost:8090/api/users/active")
	if err != nil {
		log.Printf("请求失败: %v", err)
	} else {
		defer resp2.Body.Close()
		body2, _ := io.ReadAll(resp2.Body)
		fmt.Printf("状态码: %d\n", resp2.StatusCode)
		fmt.Printf("响应: %s\n", string(body2))
		
		if resp2.StatusCode == 200 {
			var data map[string]interface{}
			json.Unmarshal(body2, &data)
			if users, ok := data["users"].([]interface{}); ok {
				fmt.Printf("用户数量: %d\n", len(users))
				for i, user := range users {
					if userMap, ok := user.(map[string]interface{}); ok {
						fmt.Printf("用户%d: %s (%s) - %s - 活跃:%v\n", i+1, 
							userMap["username"], userMap["full_name"], userMap["role"], userMap["is_active"])
					}
				}
			}
		}
	}
}
