package main

import (
	"html"
	"html/template"
	"log"
	"projectm2/internal/database"
	"projectm2/internal/handlers"
	"projectm2/internal/middleware"
	"projectm2/internal/services"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("Starting ProjectM2...")

	// 初始化数据库
	log.Println("Initializing database...")
	db, err := database.InitDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	log.Println("Database initialized successfully")

	// 初始化服务
	sessionService := services.NewSessionService()
	sessionService.StartCleanupRoutine()

	// 设置Gin模式
	gin.SetMode(gin.DebugMode)

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 添加性能优化中间件
	r.Use(func(c *gin.Context) {
		// 处理HEAD请求
		if c.Request.Method == "HEAD" {
			// 对于HEAD请求，返回相应的状态码但不返回body
			if c.Request.URL.Path == "/api/users" || c.Request.URL.Path == "/api/settings" ||
				c.Request.URL.Path == "/api/tasks" || c.Request.URL.Path == "/api/users/active" {
				c.Status(200)
				c.Abort()
				return
			}
		}

		// 添加缓存控制头
		path := c.Request.URL.Path
		if strings.HasPrefix(path, "/static/") {
			c.Header("Cache-Control", "public, max-age=86400") // 静态文件缓存1天
		} else if path == "/api/users/active" || path == "/api/time/presets" {
			c.Header("Cache-Control", "public, max-age=300") // 用户和时间预设缓存5分钟
		} else if path == "/api/tasks" {
			c.Header("Cache-Control", "public, max-age=60") // 任务数据缓存1分钟
		} else if path == "/api/user/current" || path == "/api/settings" {
			c.Header("Cache-Control", "public, max-age=600") // 用户信息和设置缓存10分钟
		}

		// 添加压缩支持
		c.Header("Vary", "Accept-Encoding")

		c.Next()
	})

	// 模板函数
	funcMap := template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"sub": func(a, b int) int {
			return a - b
		},
		"mul": func(a, b int) int {
			return a * b
		},
		"div": func(a, b int) int {
			if b != 0 {
				return a / b
			}
			return 0
		},
		"mod": func(a, b int) int {
			if b != 0 {
				return a % b
			}
			return 0
		},
		"formatTime": func(t interface{}) string {
			return "2006-01-02 15:04"
		},
		"truncate": func(s string, length int) string {
			if len(s) <= length {
				return s
			}
			return s[:length] + "..."
		},
		"stripHTML": func(s string) string {
			// 移除HTML标签，保留纯文本内容
			if s == "" {
				return ""
			}

			// 使用正则表达式移除HTML标签
			re := regexp.MustCompile(`<[^>]*>`)
			result := re.ReplaceAllString(s, "")

			// 解码HTML实体
			result = html.UnescapeString(result)

			// 移除多余的空白字符
			result = strings.TrimSpace(result)
			result = regexp.MustCompile(`\s+`).ReplaceAllString(result, " ")

			return result
		},
		"safeHTML": func(s string) template.HTML {
			return template.HTML(s)
		},
	}

	// 加载HTML模板
	r.SetFuncMap(funcMap)
	r.LoadHTMLGlob("web/templates/*")

	// 静态文件服务
	r.Static("/static", "./web/static")
	r.Static("/uploads", "./uploads")

	// 调试页面
	r.StaticFile("/debug", "./debug_api.html")

	// 初始化处理器
	h := handlers.NewHandler(db)
	authHandler := handlers.NewAuthHandler(db, sessionService)
	authMiddleware := middleware.NewAuthMiddleware(sessionService)

	// 路由设置
	setupRoutesWithAuth(r, h, authHandler, authMiddleware)

	// 启动服务器
	log.Println("Server starting on :8090")
	if err := r.Run(":8090"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupRoutesWithAuth(r *gin.Engine, h *handlers.Handler, authHandler *handlers.AuthHandler, authMiddleware *middleware.AuthMiddleware) {
	// 公开路由（无需认证）
	r.GET("/login", authHandler.LoginPage)

	// 附件预览API（不需要认证，用于图片显示）
	r.GET("/api/attachments/:id/preview", h.AttachmentHandler.PreviewFile)

	// 认证API路由
	authAPI := r.Group("/api/auth")
	{
		authAPI.POST("/login", authHandler.Login)
		authAPI.POST("/logout", authHandler.Logout)
		authAPI.POST("/register", authHandler.Register)
	}

	// 需要认证的页面路由
	protected := r.Group("/")
	protected.Use(authMiddleware.RequireAuth())
	{
		protected.GET("/", h.Home)
	}

	// 需要管理员权限的页面路由
	adminPages := r.Group("/")
	adminPages.Use(authMiddleware.RequireAuth())
	adminPages.Use(authMiddleware.RequireRole("admin"))
	{
		adminPages.GET("/users", h.UsersPage)
	}

	// 需要认证的API路由
	api := r.Group("/api")
	api.Use(authMiddleware.RequireAuth())
	{
		// 认证相关API
		api.GET("/auth/user", authHandler.GetCurrentUser)
		api.POST("/auth/change-password", authHandler.ChangePassword)

		// 任务相关API - 先注册具体路径，再注册通配符路径
		api.GET("/tasks", h.GetTasks)
		api.POST("/tasks", h.CreateTask)
		api.DELETE("/tasks/bulk", h.BulkDeleteTasks)
		api.PUT("/tasks/bulk/status", h.BulkUpdateTaskStatus)
		api.GET("/tasks/statistics", h.GetTaskStatistics)

		// 任务附件API - 在任务ID通配符之前注册
		api.GET("/tasks/:id/attachments", h.AttachmentHandler.GetTaskAttachments)

		// 任务单个操作API - 通配符路径放在最后
		api.GET("/tasks/:id", h.GetTaskByID)
		api.PUT("/tasks/:id", h.UpdateTask)
		api.DELETE("/tasks/:id", h.DeleteTask)
		api.PUT("/tasks/:id/status", h.UpdateTaskStatus)
		api.PUT("/tasks/:id/position", h.UpdateTaskPosition)

		// 时间管理API
		api.GET("/time/presets", h.GetTimePresets)

		// 任务类型相关API
		api.GET("/task-types", h.TaskTypeHandler.GetTaskTypes)
		api.GET("/task-types/:id", h.TaskTypeHandler.GetTaskTypeByID)
		api.POST("/task-types", h.TaskTypeHandler.CreateTaskType)
		api.PUT("/task-types/:id", h.TaskTypeHandler.UpdateTaskType)
		api.DELETE("/task-types/:id", h.TaskTypeHandler.DeleteTaskType)
		api.GET("/task-types/stats", h.TaskTypeHandler.GetTaskTypeStats)

		// 任务版本相关API
		api.GET("/task-versions", h.TaskVersionHandler.GetTaskVersions)
		api.GET("/task-versions/active", h.TaskVersionHandler.GetActiveTaskVersions)
		api.GET("/task-versions/:id", h.TaskVersionHandler.GetTaskVersionByID)
		api.POST("/task-versions", h.TaskVersionHandler.CreateTaskVersion)
		api.PUT("/task-versions/:id", h.TaskVersionHandler.UpdateTaskVersion)
		api.DELETE("/task-versions/:id", h.TaskVersionHandler.DeleteTaskVersion)
		api.GET("/task-versions/stats", h.TaskVersionHandler.GetTaskVersionStats)

		// 任务模板相关API
		api.GET("/task-templates", h.TaskTemplateHandler.GetTaskTemplates)
		api.GET("/task-templates/:id", h.TaskTemplateHandler.GetTaskTemplateByID)
		api.POST("/task-templates", h.TaskTemplateHandler.CreateTaskTemplate)
		api.PUT("/task-templates/:id", h.TaskTemplateHandler.UpdateTaskTemplate)
		api.DELETE("/task-templates/:id", h.TaskTemplateHandler.DeleteTaskTemplate)
		api.GET("/task-templates/stats", h.TaskTemplateHandler.GetTaskTemplateStats)

		// 附件相关API
		api.POST("/attachments/upload", h.AttachmentHandler.UploadFile)
		api.GET("/attachments/:id/download", h.AttachmentHandler.DownloadFile)
		api.DELETE("/attachments/:id", h.AttachmentHandler.DeleteAttachment)

		// 用户相关API（所有用户都可以访问）
		api.GET("/users/active", h.UserHandler.GetActiveUsers) // 获取活跃用户列表（用于任务分配）
		api.GET("/user/current", h.GetCurrentUser)             // 获取当前用户信息

		// 设置相关API
		api.GET("/settings", h.GetSettings)
		api.PUT("/settings", h.UpdateSettings)
	}

	// 需要管理员权限的API路由
	adminAPI := r.Group("/api")
	adminAPI.Use(authMiddleware.RequireAuth())
	adminAPI.Use(authMiddleware.RequireRole("admin"))
	{
		// 用户管理API（仅管理员可访问）
		adminAPI.GET("/users", h.UserHandler.GetUsers)
		adminAPI.GET("/users/:id", h.UserHandler.GetUserByID)
		adminAPI.POST("/users", h.UserHandler.CreateUser)
		adminAPI.PUT("/users/:id", h.UserHandler.UpdateUser)
		adminAPI.DELETE("/users/:id", h.UserHandler.DeleteUser)
		adminAPI.PUT("/users/:id/activate", h.UserHandler.ActivateUser)
		adminAPI.PUT("/users/:id/deactivate", h.UserHandler.DeactivateUser)
		adminAPI.GET("/users/:id/stats", h.UserHandler.GetUserTaskStats)
		adminAPI.POST("/users/avatar/upload", h.UserHandler.UploadAvatar)

		// 同步相关API
		api.GET("/sync/status", h.GetSyncStatus)
		api.GET("/sync/changes", h.GetSyncChanges)
	}
}
